{"name": "basic-example", "productName": "Electron Desktop App", "description": "Epos Service", "version": "0.0.0", "private": true, "main": "./out/main/index.js", "homepage": "https://daltonmenezes.github.io/electron-router-dom", "license": "MIT", "author": {"name": "<PERSON><PERSON>"}, "scripts": {"start": "cross-env NODE_ENV=development APP_ENV=local electron-vite preview", "dev": "cross-env NODE_ENV=development APP_ENV=local electron-vite dev --watch", "dev:prod": "cross-env NODE_ENV=production APP_ENV=prod electron-vite dev --watch", "build": "cross-env NODE_ENV=production APP_ENV=prod electron-vite build", "build:dev": "cross-env NODE_ENV=development APP_ENV=local electron-vite build", "predist": "pnpm build", "dist": "electron-builder", "dist:dev": "cross-env NODE_ENV=development APP_ENV=local electron-vite build && electron-builder", "electron:build:win": "cross-env NODE_ENV=production APP_ENV=prod electron-vite build && electron-builder --win", "convert-icon": "node scripts/convert-icon.js && node scripts/create-ico.js", "electron:build:win:dev": "cross-env NODE_ENV=development APP_ENV=local electron-vite build && electron-builder --win", "electron:build:mac": "cross-env NODE_ENV=production APP_ENV=prod electron-vite build && electron-builder --mac", "electron:build:mac:dev": "cross-env NODE_ENV=development APP_ENV=local electron-vite build && electron-builder --mac"}, "devDependencies": {"@types/form-data": "^2.2.1", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^18", "@types/react-dom": "^18", "@types/xlsx": "^0.0.35", "@vitejs/plugin-react": "^2.2.0", "cross-env": "^7.0.3", "electron": "^36.5.0", "electron-builder": "^23.6.0", "electron-vite": "^3.1.0", "eslint": "8.57.0", "png-to-ico": "^2.1.8", "postcss": "^8.4.41", "rollup-plugin-inject-process-env": "^1.3.1", "sharp": "^0.34.3", "tailwindcss": "^3.4.10", "tailwindcss-animate": "^1.0.7", "typescript": "^5", "vite": "^5.0.0", "vite-tsconfig-paths": "^3.5.2"}, "dependencies": {"@electron-toolkit/preload": "^1.0.2", "@electron-toolkit/utils": "^1.0.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-separator": "^1.1.0", "@react-pdf/renderer": "^4.3.0", "@types/bcrypt": "^5.0.2", "@types/pg": "^8.15.4", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dotenv": "^17.2.0", "electron-router-dom": "v2.1.0", "form-data": "^4.0.3", "framer-motion": "^12.19.1", "lucide-react": "^0.525.0", "papaparse": "^5.5.3", "pg": "^8.16.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "simplex-noise": "^4.0.3", "tailwind-merge": "^2.4.0", "xlsx": "^0.18.5"}, "eslintIgnore": ["dist", "out"]}