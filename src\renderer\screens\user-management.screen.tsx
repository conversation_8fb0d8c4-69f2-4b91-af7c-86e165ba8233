import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth, useIsAdmin } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';
import { safeIpcInvoke } from '../utils/electron';
import { useUserManagementOperations } from '../hooks/useUserOperations';
import { Button } from '../components/button';
import { MinimalLoader } from '../components/LoadingSpinner';
import { Modal } from '../components/modal';

interface User {
  user_id: number;
  user_ref: string;
  user_name: string;
  role_code?: string;
  role_name?: string;
  active: boolean;
  locked: boolean;
  failed_attempts: number;
  last_login?: Date;
  create_dt: Date;
  update_dt: Date;
}

interface Role {
  role_id: number;
  role_code: string;
  role_name: string;
  role_description?: string;
  active: boolean;
}

interface UserFormData {
  user_name: string;
  password: string;
  confirmPassword: string;
  role_code: string;
  active: boolean;
}

export function UserManagementScreen() {
  const { user: currentUser } = useAuth();
  const isAdmin = useIsAdmin();
  const { showNotification } = useNotification();
  const { createUser, updateUser, prepareCreateData, prepareUpdateData } = useUserManagementOperations();
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [error, setError] = useState('');
  const [userError, setUserError] = useState('');
  const [roleError, setRoleError] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [hasPreviousPage, setHasPreviousPage] = useState(false);

  // Search state
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Modal state
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [formData, setFormData] = useState<UserFormData>({
    user_name: '',
    password: '',
    confirmPassword: '',
    role_code: '',
    active: true
  });
  const [passwordFormData, setPasswordFormData] = useState({
    newPassword: '',
    confirmPassword: ''
  });

  // Check if user has admin access
  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center">
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100 mb-4">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
          <p className="text-sm text-gray-600 mb-4">
            You need administrator privileges to access user management.
          </p>
          <p className="text-xs text-gray-500">
            Your current role: {currentUser?.role_name || 'None'}
          </p>
        </div>
      </div>
    );
  }

  // Load users and roles
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadInitialData();
    }, 100); // Small delay to prevent double calls in StrictMode
    
    return () => clearTimeout(timeoutId);
  }, []);

  // Separate function to load initial data
  const loadInitialData = async () => {
    setIsLoading(true);
    await Promise.all([
      loadUsers(1, ''),
      loadRoles()
    ]);
    setIsLoading(false);
  };

  // Separate function to load users
  const loadUsers = async (page: number = currentPage, search: string = searchTerm, retryCount: number = 0) => {
    try {
      setIsLoadingUsers(true);
      setUserError('');

      console.log('🔍 [FRONTEND] Loading users with params:', { page, pageSize, search: search.trim(), retryCount });

      // Load users with timeout
      const usersPromise = Promise.race([
        safeIpcInvoke('get-users', { page, pageSize, search: search.trim() }),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Request timeout')), 15000))
      ]);

      const result = await usersPromise as any;

      console.log('📊 [FRONTEND] Users result:', result);

      if (result && result.success) {
        setUsers(result.users || []);
        if (result.pagination) {
          setCurrentPage(result.pagination.currentPage);
          setTotalPages(result.pagination.totalPages);
          setTotalRecords(result.pagination.totalRecords);
          setHasNextPage(result.pagination.hasNextPage);
          setHasPreviousPage(result.pagination.hasPreviousPage);
        }
        console.log('✅ [FRONTEND] Users loaded successfully:', result.users?.length, 'users');
      } else {
        console.error('❌ [FRONTEND] Users loading failed:', result?.message || result?.error || 'Unknown error');
        const errorMsg = result?.message || result?.error || 'Failed to load users';
        
        // Retry once if it's the first failure
        if (retryCount === 0 && (errorMsg.includes('Failed to fetch users') || errorMsg.includes('timeout'))) {
          console.log('🔄 [FRONTEND] Retrying loadUsers...');
          setTimeout(() => loadUsers(page, search, retryCount + 1), 1000);
          return;
        }
        
        setUserError(errorMsg);
      }

    } catch (error: any) {
      console.error('❌ [FRONTEND] Error loading users:', error);
      
      // Retry once if it's the first failure
      if (retryCount === 0) {
        console.log('🔄 [FRONTEND] Retrying loadUsers after catch...');
        setTimeout(() => loadUsers(page, search, retryCount + 1), 1000);
        return;
      }
      
      setUserError('Failed to load users: ' + error.message);
    } finally {
      setIsLoadingUsers(false);
    }
  };

  // Separate function to load roles
  const loadRoles = async (retryCount: number = 0) => {
    try {
      setIsLoadingRoles(true);
      setRoleError('');

      console.log('🔍 [FRONTEND] Loading roles...');

      // Load roles with timeout
      const rolesPromise = Promise.race([
        safeIpcInvoke('get-roles'),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Request timeout')), 15000))
      ]);

      const result = await rolesPromise as any;

      console.log('📊 [FRONTEND] Roles result:', result);

      if (result && result.success) {
        setRoles(result.roles || []);
        console.log('✅ [FRONTEND] Roles loaded successfully:', result.roles?.length, 'roles');
      } else {
        console.error('❌ [FRONTEND] Roles loading failed:', result?.message);
        const errorMsg = result?.message || 'Failed to load roles';
        
        // Retry once if it's the first failure
        if (retryCount === 0) {
          console.log('🔄 [FRONTEND] Retrying loadRoles...');
          setTimeout(() => loadRoles(retryCount + 1), 1000);
          return;
        }
        
        setRoleError(errorMsg);
      }

    } catch (error: any) {
      console.error('❌ [FRONTEND] Error loading roles:', error);
      
      // Retry once if it's the first failure
      if (retryCount === 0) {
        console.log('🔄 [FRONTEND] Retrying loadRoles after catch...');
        setTimeout(() => loadRoles(retryCount + 1), 1000);
        return;
      }
      
      setRoleError('Failed to load roles: ' + error.message);
    } finally {
      setIsLoadingRoles(false);
    }
  };

  // User management functions
  const handleCreateUser = useCallback(() => {
    setEditingUser(null);
    setFormData({
      user_name: '',
      password: '',
      confirmPassword: '',
      role_code: roles.length > 0 ? roles[0].role_code : '',
      active: true
    });
    setIsUserModalOpen(true);
  }, [roles]);

  const handleEditUser = useCallback((user: User) => {
    setEditingUser(user);
    setFormData({
      user_name: user.user_name,
      password: '',
      confirmPassword: '',
      role_code: user.role_code || '',
      active: user.active
    });
    setIsUserModalOpen(true);
  }, []);

  const handleDeleteUser = async (userId: number, userName: string) => {
    if (window.confirm(`Are you sure you want to deactivate user "${userName}"?`)) {
      try {
        const result = await safeIpcInvoke('delete-user', userId);
        if (result.success) {
          showNotification('User deactivated successfully');
          await loadUsers();
        } else {
          showNotification(`Failed to deactivate user: ${result.message}`, 'error');
        }
      } catch (error) {
        console.error('Error deactivating user:', error);
        showNotification('Error deactivating user', 'error');
      }
    }
  };

  const handleToggleUserLock = async (user: User) => {
    try {
      const result = await updateUser(user.user_id, {
        locked: !user.locked,
      });
      
      if (result.success) {
        showNotification(`User ${!user.locked ? 'unlocked' : 'locked'} successfully`);
        await loadUsers();
      } else {
        showNotification(`Failed to ${user.locked ? 'unlock' : 'lock'} user: ${result.message}`, 'error');
      }
    } catch (error) {
      console.error('Error toggling user lock:', error);
      showNotification('Error updating user status', 'error');
    }
  };

  const handleResetPassword = useCallback((user: User) => {
    setEditingUser(user);
    setPasswordFormData({
      newPassword: '',
      confirmPassword: ''
    });
    setIsPasswordModalOpen(true);
  }, []);

  const handleSubmitUser = async () => {
    // Validation
    if (!formData.user_name.trim()) {
      showNotification('Username is required', 'error');
      return;
    }

    if (!editingUser && !formData.password.trim()) {
      showNotification('Password is required for new users', 'error');
      return;
    }

    if (formData.password && formData.password !== formData.confirmPassword) {
      showNotification('Passwords do not match', 'error');
      return;
    }

    if (formData.password && formData.password.length < 6) {
      showNotification('Password must be at least 6 characters long', 'error');
      return;
    }

    try {
      let result;

      if (editingUser) {
        // Update existing user
        const updateData: any = {
          user_name: formData.user_name,
          role_code: formData.role_code,
          active: formData.active,
        };

        // Only include password if it's provided
        if (formData.password.trim()) {
          updateData.password = formData.password;
        }

        result = await updateUser(editingUser.user_id, updateData);
      } else {
        // Create new user
        result = await createUser({
          user_name: formData.user_name,
          password: formData.password,
          role_code: formData.role_code,
          active: formData.active,
        });
      }

      if (result.success) {
        showNotification(editingUser ? 'User updated successfully' : 'User created successfully');
        setIsUserModalOpen(false);
        await loadUsers();
      } else {
        showNotification(`Failed to ${editingUser ? 'update' : 'create'} user: ${result.message}`, 'error');
      }
    } catch (error) {
      console.error('Error saving user:', error);
      showNotification('Error saving user', 'error');
    }
  };

  const handleSubmitPasswordReset = async () => {
    if (!passwordFormData.newPassword.trim()) {
      showNotification('New password is required', 'error');
      return;
    }

    if (passwordFormData.newPassword !== passwordFormData.confirmPassword) {
      showNotification('Passwords do not match', 'error');
      return;
    }

    if (passwordFormData.newPassword.length < 6) {
      showNotification('Password must be at least 6 characters long', 'error');
      return;
    }

    if (!editingUser) return;

    try {
      const result = await safeIpcInvoke('reset-user-password', editingUser.user_id, passwordFormData.newPassword);
      
      if (result.success) {
        showNotification('Password reset successfully');
        setIsPasswordModalOpen(false);
        await loadUsers();
      } else {
        showNotification(`Failed to reset password: ${result.message}`, 'error');
      }
    } catch (error) {
      console.error('Error resetting password:', error);
      showNotification('Error resetting password', 'error');
    }
  };

  const formatDate = (dateString: string | Date) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  const getStatusBadge = (user: User) => {
    if (!user.active) {
      return <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">Inactive</span>;
    }
    if (user.locked) {
      return <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Locked</span>;
    }
    return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>;
  };

  // Event handlers
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    loadUsers(newPage, searchTerm);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
    loadUsers(1, searchTerm);
  };

  const handleSearch = useCallback((search: string) => {
    setSearchTerm(search);
    setIsSearching(true);
    
    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      loadUsers(1, search);
      setIsSearching(false);
    }, 500); // 500ms delay
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  if (isLoading) {
    return <MinimalLoader />;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">User Management</h1>
            <p className="text-gray-600">Manage system users and their roles</p>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleCreateUser}
              variant="primary"
              size="md"
              className="inline-flex items-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Add New User
            </Button>
          </div>
        </div>
      </div>

      {/* Search and Controls Section */}
      <div className="bg-white shadow-lg rounded-lg overflow-hidden mb-6">
        <div className="px-6 py-4 flex flex-col lg:flex-row gap-4">
          {/* Search Input */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
              </div>
              <input
                type="text"
                placeholder="Search by username or user reference..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className={`w-full pl-10 ${searchTerm ? 'pr-10' : 'pr-4'} py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              />
              {searchTerm && (
                <button
                  onClick={() => handleSearch('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-150"
                  type="button"
                  title="Clear search"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          </div>
          
          {/* Page Size Selector */}
          <div className="flex items-center gap-3 bg-gray-50 rounded-lg px-4 py-2">
            <label className="text-sm font-medium text-gray-700 whitespace-nowrap">Show:</label>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
              className="border-0 bg-transparent focus:outline-none focus:ring-0 text-sm font-medium text-gray-700"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
            <span className="text-sm text-gray-600 whitespace-nowrap">entries</span>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {userError && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">
                <strong>Users Error:</strong> {userError}
                <button
                  onClick={() => loadUsers()}
                  className="ml-2 text-blue-600 hover:text-blue-800 underline text-sm"
                >
                  Retry
                </button>
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Users Table */}
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">System Users</h2>
          <div className="flex items-center gap-4">
            {isLoadingUsers && (
              <div className="flex items-center gap-2 text-blue-600">
                <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                <span className="text-sm">Loading users...</span>
              </div>
            )}
            {userError && (
              <button
                onClick={() => loadUsers()}
                className="text-red-600 hover:text-red-800 underline text-sm"
              >
                Retry loading users
              </button>
            )}
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Failed Attempts
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Login
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoadingUsers && !users.length ? (
                <tr>
                  <td colSpan={6} className="px-6 py-8 text-center">
                    <div className="flex flex-col items-center justify-center gap-3">
                      <div className="animate-spin h-8 w-8 border-3 border-blue-500 border-t-transparent rounded-full"></div>
                      <p className="text-gray-500">Loading users...</p>
                    </div>
                  </td>
                </tr>
              ) : userError && !users.length ? (
                <tr>
                  <td colSpan={6} className="px-6 py-8 text-center">
                    <div className="flex flex-col items-center justify-center gap-3">
                      <svg className="h-8 w-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-gray-500">Failed to load users</p>
                      <button
                        onClick={() => loadUsers()}
                        className="text-blue-600 hover:text-blue-800 underline text-sm"
                      >
                        Try again
                      </button>
                    </div>
                  </td>
                </tr>
              ) : users.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-8 text-center">
                    <div className="flex flex-col items-center justify-center gap-3">
                      <svg className="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                      <p className="text-gray-500">
                        No users found
                      </p>
                    </div>
                  </td>
                </tr>
              ) : (
                users.map((user) => (
                  <tr key={user.user_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{user.user_name}</div>
                        <div className="text-sm text-gray-500">{user.user_ref}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                        {user.role_name || user.role_code || 'No Role'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(user)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.failed_attempts}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(user.last_login as Date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                      <div className="flex items-center justify-center gap-2">
                        <Button
                          onClick={() => handleEditUser(user)}
                          variant="secondary"
                          size="sm"
                          className="inline-flex items-center gap-1"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                          Edit
                        </Button>
                        
                        <Button
                          onClick={() => handleResetPassword(user)}
                          variant="secondary"
                          size="sm"
                          className="inline-flex items-center gap-1"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v-2L4.257 10.257a6 6 0 018.486-8.486L15 4a2 2 0 012 2z" />
                          </svg>
                          Reset
                        </Button>

                        <Button
                          onClick={() => handleToggleUserLock(user)}
                          variant={user.locked ? "primary" : "secondary"}
                          size="sm"
                          className={`inline-flex items-center gap-1 ${
                            user.locked ? 'bg-green-600 hover:bg-green-700' : 'bg-orange-600 hover:bg-orange-700 text-white'
                          }`}
                        >
                          {user.locked ? (
                            <>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                              </svg>
                              Unlock
                            </>
                          ) : (
                            <>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                              </svg>
                              Lock
                            </>
                          )}
                        </Button>

                        {!user.active && (
                          <Button
                            onClick={() => handleDeleteUser(user.user_id, user.user_name)}
                            variant="secondary"
                            size="sm"
                            className="inline-flex items-center gap-1 bg-red-600 hover:bg-red-700 text-white"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            Delete
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="bg-white shadow-lg rounded-lg overflow-hidden mt-6">
          <div className="px-6 py-4 flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalRecords)} of {totalRecords} users
            </div>
            <div className="flex items-center gap-2">
              <Button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={!hasPreviousPage}
                variant="secondary"
                size="sm"
              >
                Previous
              </Button>
              <Button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!hasNextPage}
                variant="secondary"
                size="sm"
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Roles Section */}
      <div className="mt-8 bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">System Roles</h2>
          {isLoadingRoles && (
            <div className="flex items-center gap-2 text-blue-600">
              <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
              <span className="text-sm">Loading roles...</span>
            </div>
          )}
          {roleError && (
            <button
              onClick={() => loadRoles()}
              className="text-red-600 hover:text-red-800 underline text-sm"
            >
              Retry loading roles
            </button>
          )}
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role Code
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoadingRoles && !roles.length ? (
                <tr>
                  <td colSpan={4} className="px-6 py-8 text-center">
                    <div className="flex flex-col items-center justify-center gap-3">
                      <div className="animate-spin h-8 w-8 border-3 border-blue-500 border-t-transparent rounded-full"></div>
                      <p className="text-gray-500">Loading roles...</p>
                    </div>
                  </td>
                </tr>
              ) : roleError && !roles.length ? (
                <tr>
                  <td colSpan={4} className="px-6 py-8 text-center">
                    <div className="flex flex-col items-center justify-center gap-3">
                      <svg className="h-8 w-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-gray-500">Failed to load roles</p>
                      <button
                        onClick={() => loadRoles()}
                        className="text-blue-600 hover:text-blue-800 underline text-sm"
                      >
                        Try again
                      </button>
                    </div>
                  </td>
                </tr>
              ) : roles.length === 0 ? (
                <tr>
                  <td colSpan={4} className="px-6 py-8 text-center">
                    <div className="flex flex-col items-center justify-center gap-3">
                      <svg className="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                      <p className="text-gray-500">No roles found</p>
                    </div>
                  </td>
                </tr>
              ) : (
                roles.map((role) => (
                  <tr key={role.role_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {role.role_code}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {role.role_name}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {role.role_description || 'No description'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        role.active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {role.active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create/Edit User Modal */}
      <Modal
        isOpen={isUserModalOpen}
        onClose={() => setIsUserModalOpen(false)}
        size="lg"
      >
        <div className="mb-6 max-w-[500px]">
          <h2 className="text-2xl font-bold text-gray-900">
            {editingUser ? 'Edit User' : 'Create New User'}
          </h2>
          <p className="text-gray-600 mt-1">
            {editingUser ? 'Update user information and settings' : 'Add a new user to the system'}
          </p>
        </div>
        
        <div className="space-y-6" onClick={(e) => e.stopPropagation()}>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Username <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.user_name}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                user_name: e.target.value.trim()
              }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter username"
              autoFocus
              autoComplete="off"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Password {editingUser ? '(leave blank to keep current)' : <span className="text-red-500">*</span>}
            </label>
            <input
              type="password"
              value={formData.password}
              onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter password (minimum 6 characters)"
              minLength={6}
              autoComplete="new-password"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Confirm Password {editingUser ? '(leave blank to keep current)' : <span className="text-red-500">*</span>}
            </label>
            <input
              type="password"
              value={formData.confirmPassword}
              onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Confirm password"
              minLength={6}
              autoComplete="new-password"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">Role</label>
            <select
              value={formData.role_code}
              onChange={(e) => setFormData(prev => ({ ...prev, role_code: e.target.value }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">No Role</option>
              {roles.filter(role => role.active).map((role) => (
                <option key={role.role_id} value={role.role_code}>
                  {role.role_name} ({role.role_code})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">Status</label>
            <select
              value={formData.active.toString()}
              onChange={(e) => setFormData(prev => ({ ...prev, active: e.target.value === 'true' }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>

          <div className="flex gap-3 pt-6">
            <Button
              onClick={handleSubmitUser}
              variant="primary"
              size="md"
              className="flex-1"
            >
              {editingUser ? 'Update User' : 'Create User'}
            </Button>
            <Button
              onClick={() => setIsUserModalOpen(false)}
              variant="secondary"
              size="md"
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </div>
      </Modal>

      {/* Password Reset Modal */}
      <Modal
        isOpen={isPasswordModalOpen}
        onClose={() => setIsPasswordModalOpen(false)}
        size="lg"
      >
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Reset Password</h2>
          <p className="text-gray-600 mt-1">
            Reset password for user: <span className="font-semibold">{editingUser?.user_name}</span>
          </p>
        </div>
        
        <div className="space-y-6" onClick={(e) => e.stopPropagation()}>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              New Password <span className="text-red-500">*</span>
            </label>
            <input
              type="password"
              value={passwordFormData.newPassword}
              onChange={(e) => setPasswordFormData(prev => ({ ...prev, newPassword: e.target.value }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter new password (minimum 6 characters)"
              minLength={6}
              autoFocus
              autoComplete="new-password"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Confirm New Password <span className="text-red-500">*</span>
            </label>
            <input
              type="password"
              value={passwordFormData.confirmPassword}
              onChange={(e) => setPasswordFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Confirm new password"
              minLength={6}
              autoComplete="new-password"
            />
          </div>

          <div className="flex gap-3 pt-6">
            <Button
              onClick={handleSubmitPasswordReset}
              variant="primary"
              size="md"
              className="flex-1"
            >
              Reset Password
            </Button>
            <Button
              onClick={() => setIsPasswordModalOpen(false)}
              variant="secondary"
              size="md"
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );


}
