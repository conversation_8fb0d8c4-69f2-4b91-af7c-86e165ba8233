import React, { useState, useEffect, useCallback } from "react";
import { safeIpcInvoke } from "../utils/electron";
import { Button } from "../components/button";
import { useAuth } from "../contexts/AuthContext";
import { useNotification } from "../contexts/NotificationContext";
import { useMerchantOperations } from "../hooks/useUserOperations";
import { useMasterDataAccess } from "../hooks/useRoleAccess";
import { RoleBasedComponent } from "../components/RoleBasedComponent";
import { downloadCSV, MERCHANT_EXPORT_HEADERS, formatDateForExport, formatBooleanForExport } from "../utils/exportUtils";
import { MerchantModal } from "../components/merchant/MerchantModal";
import type {
  Merchant,
  MerchantBank,
  MerchantWechat,
  MerchantUnionpay,
  Bank,
  Group,
  Zone,
  Product,
  Category
} from "../types/merchant";

interface MerchantResponse {
  success: boolean;
  message: string;
  data?: Merchant | Merchant[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

export function MerchantManagementScreen() {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const { canCreate, canUpdate, canDelete, canExport, hasWriteAccess, userRole } = useMasterDataAccess();
  const {
    createMerchant,
    updateMerchant,
    createMerchantBank,
    updateMerchantBank,
    createMerchantWechat,
    updateMerchantWechat,
    createMerchantUnionpay,
    updateMerchantUnionpay,
    prepareCreateData,
    prepareUpdateData
  } = useMerchantOperations();
  
  // State for merchants list
  const [merchants, setMerchants] = useState<Merchant[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(() => {
    const saved = localStorage.getItem('merchantManagement_pageSize');
    return saved ? parseInt(saved) : 10;
  });
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [hasPreviousPage, setHasPreviousPage] = useState(false);
  
  // Search state
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  
  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingMerchant, setEditingMerchant] = useState<Merchant | null>(null);
  const [isLoadingMerchantData, setIsLoadingMerchantData] = useState(false);
  
  // Form data state
  const [formData, setFormData] = useState<Omit<Merchant, "merchant_id" | "create_dt" | "update_dt">>(() =>
    prepareCreateData({
      merchant_name: "",
      merchant_type: "main",
      parent_merchant_id: undefined,
      active: true,
    })
  );
  
  // Master data state for dropdowns
  const [banks, setBanks] = useState<Bank[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [zones, setZones] = useState<Zone[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [mainMerchants, setMainMerchants] = useState<{merchant_id: number, merchant_name: string}[]>([]);
  
  // Related data state
  const [merchantBanks, setMerchantBanks] = useState<MerchantBank[]>([]);
  const [merchantWechat, setMerchantWechat] = useState<MerchantWechat | null>(null);
  const [merchantUnionpay, setMerchantUnionpay] = useState<MerchantUnionpay | null>(null);
  
  // Handle form input changes - memoized to prevent losing focus
  const handleInputChange = useCallback((field: keyof Merchant, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  }, []);
  
  // Load merchants with pagination and search
  const loadMerchants = useCallback(async (page: number = currentPage, search: string = debouncedSearchTerm) => {
    setIsLoading(true);
    
    try {
      const response: MerchantResponse = await safeIpcInvoke("get-merchants", {
        page,
        pageSize,
        search,
        sortBy: "merchant_id",
        sortOrder: "ASC",
      });
      
      if (response.success) {
        setMerchants(response.data as Merchant[]);
        
        if (response.pagination) {
          setCurrentPage(response.pagination.currentPage);
          setTotalPages(response.pagination.totalPages);
          setTotalRecords(response.pagination.totalRecords);
          setHasNextPage(response.pagination.hasNextPage);
          setHasPreviousPage(response.pagination.hasPreviousPage);
        }
      } else {
        showNotification(
          response.message || "Failed to load merchants",
          "error"
        );
      }
    } catch (error) {
      console.error("Error loading merchants:", error);
      showNotification(
        "Error loading merchants",
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, debouncedSearchTerm, pageSize, showNotification]);
  
  // Initialize and load master data for dropdowns
  const loadMasterData = useCallback(async () => {
    try {

      // Load banks
      const bankResponse = await safeIpcInvoke("get-banks", {
        pageSize: 100,
        sortBy: "bank_code",
      });
      if (bankResponse.success) {
        setBanks(bankResponse.data as Bank[]);
      }

      // Load groups
      const groupResponse = await safeIpcInvoke("get-groups");
      if (groupResponse.success) {
        setGroups(groupResponse.data as Group[]);
      }

      // Load zones
      const zoneResponse = await safeIpcInvoke("get-zones");
      if (zoneResponse.success) {
        setZones(zoneResponse.data as Zone[]);
      }

      // Load products
      const productResponse = await safeIpcInvoke("get-products");
      if (productResponse.success) {
        setProducts(productResponse.data as Product[]);
      }

      // Load categories
      const categoryResponse = await safeIpcInvoke("get-categories");
      if (categoryResponse.success) {
        setCategories(categoryResponse.data as Category[]);
      }

      // Load main merchants for sub merchant dropdown
      const mainMerchantResponse = await safeIpcInvoke("get-main-merchants");
      if (mainMerchantResponse.success) {
        setMainMerchants(mainMerchantResponse.data as {merchant_id: number, merchant_name: string}[]);
      }
    } catch (error) {
      console.error("Error loading master data:", error);
    }
  }, []);
  
  // Initial data loading
  useEffect(() => {
    const initializeData = async () => {
      setIsInitializing(true);
      try {
        await Promise.all([loadMerchants(), loadMasterData()]);
      } catch (error) {
        console.error("Error initializing merchant data:", error);
      } finally {
        setIsInitializing(false);
      }
    };
    
    initializeData();
  }, []);
  
  // Search term debouncing
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page on search
    }, 500);
    
    return () => clearTimeout(timer);
  }, [searchTerm]);
  
  // Reload when debounced search term changes
  useEffect(() => {
    loadMerchants(1, debouncedSearchTerm);
  }, [debouncedSearchTerm]);
  
  // Save page size preference
  useEffect(() => {
    localStorage.setItem('merchantManagement_pageSize', pageSize.toString());
  }, [pageSize]);
  
  // Handle page size change
  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = parseInt(e.target.value);
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };
  
  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadMerchants(page);
  };
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Load merchant related data (banks, WeChat, UnionPay) in parallel
  const loadMerchantRelatedData = async (merchantId: number) => {
    setIsLoadingMerchantData(true);
    try {
      console.log(`🔄 Loading related data for merchant ID: ${merchantId}`);
      const startTime = performance.now();

      // Run all three API calls in parallel for better performance
      const [bankResponse, wechatResponse, unionpayResponse] = await Promise.all([
        safeIpcInvoke("get-merchant-banks", merchantId),
        safeIpcInvoke("get-merchant-wechat", merchantId),
        safeIpcInvoke("get-merchant-unionpay", merchantId)
      ]);

      const endTime = performance.now();
      console.log(`⚡ Parallel loading completed in ${(endTime - startTime).toFixed(2)}ms`);

      // Process bank accounts
      if (bankResponse.success) {
        setMerchantBanks(bankResponse.data || []);
        console.log(`✅ Loaded ${bankResponse.data?.length || 0} bank accounts`);
      } else {
        setMerchantBanks([]);
        console.warn('⚠️ Failed to load bank accounts:', bankResponse.message);
      }

      // Process WeChat settings
      if (wechatResponse.success && wechatResponse.data && wechatResponse.data.length > 0) {
        setMerchantWechat(wechatResponse.data[0]);
        console.log('✅ Loaded WeChat settings');
      } else {
        setMerchantWechat(null);
        console.log('ℹ️ No WeChat settings found');
      }

      // Process UnionPay settings
      if (unionpayResponse.success && unionpayResponse.data && unionpayResponse.data.length > 0) {
        setMerchantUnionpay(unionpayResponse.data[0]);
        console.log('✅ Loaded UnionPay settings');
      } else {
        setMerchantUnionpay(null);
        console.log('ℹ️ No UnionPay settings found');
      }

      console.log(`🎉 Completed loading related data for merchant ID: ${merchantId}`);
    } catch (error) {
      console.error("❌ Error loading merchant related data:", error);
      // Set default values on error
      setMerchantBanks([]);
      setMerchantWechat(null);
      setMerchantUnionpay(null);
    } finally {
      setIsLoadingMerchantData(false);
    }
  };

  // Alternative optimized loading function using single API call
  const loadMerchantRelatedDataOptimized = async (merchantId: number) => {
    setIsLoadingMerchantData(true);
    try {
      console.log(`🚀 Loading complete data for merchant ID: ${merchantId} (OPTIMIZED)`);
      const startTime = performance.now();

      // Single API call to get all related data
      const response = await safeIpcInvoke("get-merchant-complete-data", merchantId);

      const endTime = performance.now();
      console.log(`⚡ Optimized loading completed in ${(endTime - startTime).toFixed(2)}ms`);

      if (response.success && response.data) {
        // Set bank accounts
        setMerchantBanks(response.data.banks || []);
        console.log(`✅ Loaded ${response.data.banks?.length || 0} bank accounts`);

        // Set WeChat settings
        setMerchantWechat(response.data.wechat || null);
        console.log(response.data.wechat ? '✅ Loaded WeChat settings' : 'ℹ️ No WeChat settings found');

        // Set UnionPay settings
        setMerchantUnionpay(response.data.unionpay || null);
        console.log(response.data.unionpay ? '✅ Loaded UnionPay settings' : 'ℹ️ No UnionPay settings found');

        console.log(`🎉 Completed optimized loading for merchant ID: ${merchantId}`);
      } else {
        console.warn('⚠️ Failed to load complete merchant data:', response.message);
        // Set default values on failure
        setMerchantBanks([]);
        setMerchantWechat(null);
        setMerchantUnionpay(null);
      }
    } catch (error) {
      console.error("❌ Error loading optimized merchant data:", error);
      // Fallback to parallel loading if optimized fails
      console.log("🔄 Falling back to parallel loading method");
      await loadMerchantRelatedData(merchantId);
      return;
    } finally {
      setIsLoadingMerchantData(false);
    }
  };

  // Export merchants to CSV
  const handleExport = () => {
    if (!canExport) {
      showNotification("You don't have permission to export data", "error");
      return;
    }

    // Format data for export
    const exportData = merchants.map(merchant => ({
      ...merchant,
      active: formatBooleanForExport(merchant.active),
      create_dt: formatDateForExport(merchant.create_dt),
      update_dt: formatDateForExport(merchant.update_dt)
    }));

    // Download CSV
    // downloadCSV(exportData, `merchants-${new Date().toISOString().split('T')[0]}.csv`, MERCHANT_EXPORT_HEADERS);
    showNotification("Merchants exported successfully", "success");
  };
  
  // Create new merchant
  const handleCreate = () => {
    if (!canCreate) {
      showNotification("You don't have permission to create merchants", "error");
      return;
    }
    setEditingMerchant(null);
    setFormData(prepareCreateData({
      merchant_name: "",
      merchant_type: "main",
      parent_merchant_id: undefined,
      active: true,
    }));
    setMerchantBanks([]);
    setMerchantWechat(null);
    setMerchantUnionpay(null);
    setIsModalOpen(true);
  };
  
  // Edit existing merchant
  const handleEdit = async (merchant: Merchant) => {
    if (!canUpdate) {
      showNotification("You don't have permission to edit merchants", "error");
      return;
    }
    setEditingMerchant(merchant);
    setFormData(prepareUpdateData({
      merchant_name: merchant.merchant_name,
      merchant_vat: merchant.merchant_vat || "",
      merchant_type: merchant.merchant_type,
      parent_merchant_id: merchant.parent_merchant_id,
      merchant_sub_name: merchant.merchant_sub_name || "",
      merchant_mcc: merchant.merchant_mcc || "",
      merchant_id_wechat: merchant.merchant_id_wechat || "",
      phone: merchant.phone || "",
      email: merchant.email || "",
      address: merchant.address || "",
      zipcode: merchant.zipcode || "",
      remark: merchant.remark || "",
      invoice_name: merchant.invoice_name || "",
      invoice_tax: merchant.invoice_tax || "",
      invoice_address: merchant.invoice_address || "",
      contact_person: merchant.contact_person || "",
      contact_email: merchant.contact_email || "",
      contact_phone: merchant.contact_phone || "",
      contact_fax: merchant.contact_fax || "",
      group_id: merchant.group_id,
      zone_id: merchant.zone_id,
      product_id: merchant.product_id,
      category_id: merchant.category_id,
      rate_min_transfer: merchant.rate_min_transfer,
      transfer_fee: merchant.transfer_fee,
      settlement_fee: merchant.settlement_fee,
      withholding_tax: merchant.withholding_tax,
      active: merchant.active,
      create_by: merchant.create_by,
    }));

    // Load related data using optimized parallel loading
    if (merchant.merchant_id) {
      await loadMerchantRelatedData(merchant.merchant_id);
    }

    setIsModalOpen(true);
  };

  // View merchant details (for viewer role)
  const handleView = async (merchant: Merchant) => {
    setEditingMerchant(merchant);
    setFormData(prepareUpdateData({
      merchant_name: merchant.merchant_name,
      merchant_vat: merchant.merchant_vat || "",
      merchant_type: merchant.merchant_type,
      parent_merchant_id: merchant.parent_merchant_id,
      merchant_sub_name: merchant.merchant_sub_name || "",
      merchant_mcc: merchant.merchant_mcc || "",
      merchant_id_wechat: merchant.merchant_id_wechat || "",
      phone: merchant.phone || "",
      email: merchant.email || "",
      address: merchant.address || "",
      zipcode: merchant.zipcode || "",
      remark: merchant.remark || "",
      invoice_name: merchant.invoice_name || "",
      invoice_tax: merchant.invoice_tax || "",
      invoice_address: merchant.invoice_address || "",
      contact_person: merchant.contact_person || "",
      contact_email: merchant.contact_email || "",
      contact_phone: merchant.contact_phone || "",
      contact_fax: merchant.contact_fax || "",
      group_id: merchant.group_id,
      zone_id: merchant.zone_id,
      product_id: merchant.product_id,
      category_id: merchant.category_id,
      rate_min_transfer: merchant.rate_min_transfer,
      transfer_fee: merchant.transfer_fee,
      settlement_fee: merchant.settlement_fee,
      withholding_tax: merchant.withholding_tax,
      active: merchant.active,
      create_by: merchant.create_by,
    }));

    // Load related data using optimized parallel loading
    if (merchant.merchant_id) {
      await loadMerchantRelatedData(merchant.merchant_id);
    }

    setIsModalOpen(true);
  };

  // Delete merchant
  const handleDelete = async (merchant: Merchant) => {
    if (!canDelete) {
      showNotification("You don't have permission to delete merchants", "error");
      return;
    }

    if (!merchant.merchant_id) return;

    if (window.confirm(`Are you sure you want to delete merchant "${merchant.merchant_name}"?`)) {
      try {
        const response = await safeIpcInvoke("delete-merchant", merchant.merchant_id);

        if (response.success) {
          showNotification(
            "Merchant deleted successfully",
            "success"
          );
          loadMerchants(); // Reload the list
        } else {
          showNotification(
            response.message || "Failed to delete merchant",
            "error"
          );
        }
      } catch (error) {
        console.error("Error deleting merchant:", error);
        showNotification(
          "Error deleting merchant",
          "error"
        );
      }
    }
  };

  // Submit form - Updated to handle validation without form event
  const handleSubmit = async (e?: React.FormEvent) => {
    // Prevent default if event is provided (for compatibility)
    if (e) {
      e.preventDefault();
    }

    try {
      let response;

      if (editingMerchant && editingMerchant.merchant_id) {
        // Update existing merchant
        response = await updateMerchant(editingMerchant.merchant_id, formData);
      } else {
        // Create new merchant
        response = await createMerchant(formData);
      }

      if (response.success) {
        showNotification(
          editingMerchant
            ? "Merchant updated successfully"
            : "Merchant created successfully",
          "success"
        );
        setIsModalOpen(false);
        await loadMerchants(); // Reload the list
      } else {
        showNotification(
          `Failed to ${editingMerchant ? "update" : "create"} merchant: ${
            response.message
          }`,
          "error"
        );
      }
    } catch (error) {
      console.error("Error saving merchant:", error);
      showNotification(
        "Error saving merchant",
        "error"
      );
    }
  };

  // Render merchant type badge
  const renderMerchantTypeBadge = (type: string) => {
    if (type === "main") {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          Main
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          Sub
        </span>
      );
    }
  };

  // Show initial loading screen
  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 sm:p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            Loading merchant management data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Merchant Management</h1>
              <p className="text-gray-600 mt-1">
                Manage merchant information and payment settings
              </p>
              {/* <p className="text-sm text-blue-600 mt-1">
                Role: {userRole?.toUpperCase()} | Access: {hasWriteAccess ? 'Read/Write' : 'Read Only'}
              </p> */}
            </div>
            <div className="flex gap-3">
              {/* Export Button - Available for all roles */}
              {/* <RoleBasedComponent requiredPermission="canExport">
                <Button
                  onClick={handleExport}
                  variant="secondary"
                  size="md"
                  className="inline-flex items-center gap-2"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  Export CSV
                </Button>
              </RoleBasedComponent> */}

              {/* Add Button - Only for Admin/Maker */}
              <RoleBasedComponent requiredPermission="canCreate">
                <Button
                  onClick={handleCreate}
                  variant="primary"
                  size="md"
                  className="inline-flex items-center gap-2"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Add New Merchant
                </Button>
              </RoleBasedComponent>
            </div>
          </div>
        </div>

        {/* Search and Controls Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search by name, VAT, email, or phone..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm("")}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                    type="button"
                  >
                    <svg
                      className="h-5 w-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                )}
              </div>
            </div>

            {/* Page Size Selector */}
            <div className="flex items-center gap-3 bg-gray-50 rounded-lg px-4 py-2">
              <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                Show:
              </label>
              <select
                value={pageSize}
                onChange={handlePageSizeChange}
                className="border-0 bg-transparent focus:outline-none focus:ring-0 text-sm font-medium text-gray-700"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
              <span className="text-sm text-gray-600 whitespace-nowrap">
                entries
              </span>
            </div>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12">
            <div className="flex flex-col items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 border-t-blue-500"></div>
              <span className="mt-4 text-gray-600 font-medium">
                Loading merchants...
              </span>
            </div>
          </div>
        )}

        {/* Merchants Table */}
        {!isLoading && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {merchants.length === 0 ? (
              <div className="px-6 py-12 text-center">
                <div className="flex flex-col items-center justify-center">
                  <svg
                    className="w-12 h-12 text-gray-400 mb-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                    />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No merchants found
                  </h3>
  
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Merchant Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Parent Merchant
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        VAT
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Contact
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {merchants.map((merchant) => (
                      <tr
                        key={merchant.merchant_id || merchant.merchant_name}
                        className="hover:bg-gray-50 transition-colors"
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          #{merchant.merchant_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {merchant.merchant_name}
                            </div>
                            {merchant.merchant_sub_name && (
                              <div className="text-sm text-gray-500">
                                {merchant.merchant_sub_name}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {renderMerchantTypeBadge(merchant.merchant_type)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {merchant.merchant_type === 'sub' && merchant.parent_merchant_name
                            ? merchant.parent_merchant_name
                            : merchant.merchant_type === 'sub'
                              ? <span className="text-red-500">Not Set</span>
                              : "-"
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {merchant.merchant_vat || "-"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {merchant.email || "-"}
                          </div>
                          <div className="text-sm text-gray-500">
                            {merchant.phone || "-"}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              merchant.active
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {merchant.active ? "Active" : "Inactive"}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            {/* View Button - For Viewer Role */}
                            <RoleBasedComponent requiredPermission="hasReadOnlyAccess">
                              <Button
                                onClick={() => handleView(merchant)}
                                variant="secondary"
                                size="sm"
                                className="inline-flex items-center gap-1"
                              >
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                  />
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                  />
                                </svg>
                                View
                              </Button>
                            </RoleBasedComponent>

                            {/* Edit Button - Only for Admin/Maker */}
                            <RoleBasedComponent requiredPermission="canUpdate">
                              <Button
                                onClick={() => handleEdit(merchant)}
                                variant="secondary"
                                size="sm"
                                className="inline-flex items-center gap-1"
                              >
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                  />
                                </svg>
                                Edit
                              </Button>
                            </RoleBasedComponent>

                            {/* Delete Button - Only for Admin/Maker */}
                            <RoleBasedComponent requiredPermission="canDelete">
                              <Button
                                onClick={() => handleDelete(merchant)}
                                variant="danger"
                                size="sm"
                                className="inline-flex items-center gap-1"
                              >
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                  />
                                </svg>
                                Delete
                              </Button>
                            </RoleBasedComponent>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Pagination Controls */}
        {!isLoading && totalPages > 1 && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-sm text-gray-600">
                Showing{" "}
                <span className="font-medium">
                  {merchants.length > 0 ? (currentPage - 1) * pageSize + 1 : 0}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(currentPage * pageSize, totalRecords)}
                </span>{" "}
                of <span className="font-medium">{totalRecords}</span> merchants
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={!hasPreviousPage}
                  variant="secondary"
                  size="sm"
                >
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        variant={currentPage === pageNum ? "primary" : "secondary"}
                        size="sm"
                        className="min-w-[2.5rem]"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!hasNextPage}
                  variant="secondary"
                  size="sm"
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Merchant Modal */}
      <MerchantModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        editingMerchant={editingMerchant}
        formData={formData}
        onInputChange={handleInputChange}
        merchantBanks={merchantBanks}
        setMerchantBanks={setMerchantBanks}
        merchantWechat={merchantWechat}
        setMerchantWechat={setMerchantWechat}
        merchantUnionpay={merchantUnionpay}
        setMerchantUnionpay={setMerchantUnionpay}
        banks={banks}
        groups={groups}
        zones={zones}
        products={products}
        categories={categories}
        mainMerchants={mainMerchants}
        onSubmit={handleSubmit}
        onRefresh={loadMerchants}
        viewOnly={!hasWriteAccess}
      />
    </div>
  );
}
